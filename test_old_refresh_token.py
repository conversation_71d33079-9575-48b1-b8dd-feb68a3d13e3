#!/usr/bin/env python3
"""
测试旧的refresh token是否还有效
"""

import os
import json
import httpx
import asyncio
from dotenv import load_dotenv

load_dotenv()

async def test_old_refresh_token():
    """测试旧的refresh token"""
    refresh_token = os.getenv("KIRO_REFRESH_TOKEN_1")
    
    if not refresh_token:
        print("❌ 未找到refresh token")
        return
    
    print("🔍 测试旧的refresh token...")
    print(f"Refresh Token: {refresh_token[:50]}...")
    
    refresh_url = "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                refresh_url,
                json={"refreshToken": refresh_token},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Refresh token仍然有效!")
                data = response.json()
                print(f"新的access token: {data.get('accessToken', '')[:50]}...")
                print(f"新的refresh token: {data.get('refreshToken', '')[:50]}...")
                
                # 更新.env文件
                if data.get('accessToken'):
                    print("\n🔧 更新.env文件...")
                    env_content = f"""# Ki2API 多账号轮训配置
# 刷新后的token

# 基础配置
API_KEY=ki2api-key-2024

# 账号1 - 刷新后 (Github认证)
KIRO_ACCESS_TOKEN_1={data.get('accessToken')}
KIRO_REFRESH_TOKEN_1={data.get('refreshToken', refresh_token)}

# 账号2 - 待配置
# KIRO_ACCESS_TOKEN_2=your_access_token_2_here
# KIRO_REFRESH_TOKEN_2=your_refresh_token_2_here
"""
                    with open('.env', 'w', encoding='utf-8') as f:
                        f.write(env_content)
                    print("✅ .env文件已更新")
                
            elif response.status_code == 400:
                print("❌ Refresh token无效或已过期")
                try:
                    error_data = response.json()
                    print(f"错误详情: {error_data}")
                except:
                    print(f"错误文本: {response.text}")
            else:
                print(f"❌ 未知错误: {response.status_code}")
                print(f"响应: {response.text}")
                
    except Exception as e:
        print(f"❌ 请求失败: {e}")

async def main():
    print("🚀 测试旧refresh token的有效性")
    print("=" * 50)
    await test_old_refresh_token()
    print("=" * 50)
    print("💡 如果refresh token已过期，请:")
    print("1. 重新登录 https://kiro.dev")
    print("2. 获取新的token")
    print("3. 更新.env文件")

if __name__ == "__main__":
    asyncio.run(main())
