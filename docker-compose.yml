version: '3.8'

services:
  ki2api:
    build: .
    ports:
      - "8989:8989"
    environment:
      - API_KEY=ki2api-key-2024
      # 多账号轮训配置示例（取消注释并填入真实token）
      # - KIRO_ACCESS_TOKEN_1=your_access_token_1
      # - KIRO_REFRESH_TOKEN_1=your_refresh_token_1
      # - KIRO_ACCESS_TOKEN_2=your_access_token_2
      # - KIRO_REFRESH_TOKEN_2=your_refresh_token_2
      # - KIRO_ACCESS_TOKEN_3=your_access_token_3
      # - KIRO_REFRESH_TOKEN_3=your_refresh_token_3
    volumes:
      # 自动挂载token目录，实现零配置
      - ~/.aws/sso/cache:/root/.aws/sso/cache:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8989/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s