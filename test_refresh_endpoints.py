#!/usr/bin/env python3
"""
测试不同的token刷新端点和格式
"""

import os
import json
import httpx
import asyncio
from dotenv import load_dotenv

load_dotenv()

class RefreshTester:
    def __init__(self):
        self.refresh_token = os.getenv("KIRO_REFRESH_TOKEN_1")
        
        # 可能的刷新端点
        self.endpoints = [
            "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken",
            "https://auth.kiro.dev/refreshToken", 
            "https://api.kiro.dev/auth/refresh",
            "https://kiro.dev/api/auth/refresh",
            "https://auth.desktop.kiro.dev/refreshToken",
            "https://us-east-1.auth.desktop.kiro.dev/refreshToken"
        ]
        
        # 不同的请求格式
        self.request_formats = [
            {"refreshToken": self.refresh_token},
            {"refresh_token": self.refresh_token},
            {"token": self.refresh_token},
            {
                "grant_type": "refresh_token",
                "refresh_token": self.refresh_token
            },
            {
                "refreshToken": self.refresh_token,
                "grant_type": "refresh_token"
            }
        ]

    async def test_endpoint(self, endpoint, request_data, format_name):
        """测试单个端点和格式组合"""
        print(f"  📡 测试格式: {format_name}")
        
        try:
            async with httpx.AsyncClient() as client:
                # 尝试不同的Content-Type
                headers_list = [
                    {"Content-Type": "application/json"},
                    {"Content-Type": "application/x-www-form-urlencoded"},
                    {
                        "Content-Type": "application/json",
                        "User-Agent": "Kiro-Desktop/1.0"
                    }
                ]
                
                for i, headers in enumerate(headers_list, 1):
                    print(f"    🔧 尝试头部 {i}: {headers}")
                    
                    try:
                        if headers.get("Content-Type") == "application/x-www-form-urlencoded":
                            # 使用form data
                            response = await client.post(
                                endpoint,
                                data=request_data,
                                headers=headers,
                                timeout=30
                            )
                        else:
                            # 使用JSON
                            response = await client.post(
                                endpoint,
                                json=request_data,
                                headers=headers,
                                timeout=30
                            )
                        
                        print(f"      状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("      🎉 成功!")
                            try:
                                data = response.json()
                                print(f"      响应: {json.dumps(data, indent=6)}")
                                return data
                            except:
                                print(f"      响应文本: {response.text}")
                                return response.text
                        else:
                            print(f"      ❌ 失败: {response.status_code}")
                            try:
                                error_data = response.json()
                                print(f"      错误: {json.dumps(error_data, indent=6)}")
                            except:
                                print(f"      错误文本: {response.text[:200]}...")
                    
                    except Exception as e:
                        print(f"      ❌ 请求异常: {e}")
                        
        except Exception as e:
            print(f"    ❌ 连接异常: {e}")
        
        return None

    async def test_all_combinations(self):
        """测试所有端点和格式的组合"""
        print("🔍 开始测试所有刷新端点和格式组合...")
        print("=" * 80)
        
        if not self.refresh_token:
            print("❌ 未找到refresh token")
            return
        
        print(f"🔑 使用refresh token: {self.refresh_token[:50]}...")
        print()
        
        success_results = []
        
        for endpoint in self.endpoints:
            print(f"🌐 测试端点: {endpoint}")
            
            for i, request_data in enumerate(self.request_formats, 1):
                format_name = f"格式{i}: {list(request_data.keys())}"
                result = await self.test_endpoint(endpoint, request_data, format_name)
                
                if result:
                    success_results.append({
                        "endpoint": endpoint,
                        "format": request_data,
                        "result": result
                    })
                    print(f"      ✅ 找到工作的组合!")
                    break  # 找到工作的格式就跳出
            
            print()
        
        print("=" * 80)
        print("📊 测试结果总结:")
        
        if success_results:
            print(f"✅ 找到 {len(success_results)} 个工作的组合:")
            for i, result in enumerate(success_results, 1):
                print(f"\n{i}. 端点: {result['endpoint']}")
                print(f"   格式: {result['format']}")
                print(f"   结果: {type(result['result'])}")
        else:
            print("❌ 没有找到工作的组合")
            print("\n💡 可能的原因:")
            print("1. Refresh token已过期")
            print("2. 刷新端点已更改")
            print("3. 需要特殊的认证头或参数")
            print("4. 服务暂时不可用")

async def main():
    tester = RefreshTester()
    await tester.test_all_combinations()

if __name__ == "__main__":
    asyncio.run(main())
