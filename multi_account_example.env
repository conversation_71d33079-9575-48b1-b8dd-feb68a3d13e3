# Ki2API 多账号轮训配置示例
# 复制此文件为 .env 并填入真实的token值

# 基础配置
API_KEY=ki2api-key-2024

# 多账号配置 - 支持最多10个账号
# 取消注释并填入真实的token值

# 账号1
KIRO_ACCESS_TOKEN_1=your_access_token_1_here
KIRO_REFRESH_TOKEN_1=your_refresh_token_1_here

# 账号2  
KIRO_ACCESS_TOKEN_2=your_access_token_2_here
KIRO_REFRESH_TOKEN_2=your_refresh_token_2_here

# 账号3
KIRO_ACCESS_TOKEN_3=your_access_token_3_here
KIRO_REFRESH_TOKEN_3=your_refresh_token_3_here

# 可以继续添加更多账号...
# KIRO_ACCESS_TOKEN_4=your_access_token_4_here
# KIRO_REFRESH_TOKEN_4=your_refresh_token_4_here

# 使用说明：
# 1. 将此文件重命名为 .env
# 2. 填入真实的Kiro账号token
# 3. 启动服务：docker-compose up -d
# 4. 系统会自动在配置的账号间轮换使用
