#!/usr/bin/env python3
"""
AWS EventStream 格式解析器
专门用于解析Kiro API返回的二进制事件流数据
"""

import json
import struct
import re
from typing import List, Dict, Any

class EventStreamParser:
    """AWS EventStream格式解析器"""
    
    def __init__(self):
        self.content_parts = []
    
    def parse_binary_eventstream(self, data: bytes) -> str:
        """解析二进制EventStream数据"""
        content_parts = []
        
        try:
            # 方法1: 使用正则表达式提取JSON内容
            # 查找所有的 {"content":"..."} 模式
            json_pattern = rb'\{"content":"([^"]+)"\}'
            matches = re.findall(json_pattern, data)
            
            if matches:
                # 解码找到的内容
                for match in matches:
                    try:
                        text = match.decode('utf-8')
                        content_parts.append(text)
                    except UnicodeDecodeError:
                        # 尝试其他编码
                        try:
                            text = match.decode('latin-1')
                            content_parts.append(text)
                        except:
                            continue
            
            if content_parts:
                return ''.join(content_parts)
            
            # 方法2: 尝试解析EventStream结构
            return self._parse_eventstream_structure(data)
            
        except Exception as e:
            print(f"解析EventStream时出错: {e}")
            return self._fallback_parse(data)
    
    def _parse_eventstream_structure(self, data: bytes) -> str:
        """尝试解析EventStream的结构"""
        content_parts = []
        offset = 0
        
        while offset < len(data):
            try:
                # EventStream消息格式:
                # 4字节: 总长度
                # 4字节: 头部长度
                # 头部数据
                # 负载数据
                # 4字节: CRC
                
                if offset + 8 > len(data):
                    break
                
                # 读取消息长度和头部长度
                total_length = struct.unpack('>I', data[offset:offset+4])[0]
                headers_length = struct.unpack('>I', data[offset+4:offset+8])[0]
                
                if total_length == 0 or total_length > len(data) - offset:
                    offset += 1
                    continue
                
                # 计算负载开始位置
                payload_start = offset + 8 + headers_length
                payload_length = total_length - 8 - headers_length - 4  # 减去CRC
                
                if payload_start + payload_length > len(data):
                    offset += 1
                    continue
                
                # 提取负载数据
                payload = data[payload_start:payload_start + payload_length]
                
                # 尝试解析JSON
                try:
                    payload_str = payload.decode('utf-8')
                    if payload_str.strip():
                        json_data = json.loads(payload_str)
                        if 'content' in json_data:
                            content_parts.append(json_data['content'])
                except:
                    pass
                
                offset += total_length
                
            except Exception:
                offset += 1
        
        return ''.join(content_parts)
    
    def _fallback_parse(self, data: bytes) -> str:
        """备用解析方法"""
        content_parts = []
        
        try:
            # 尝试直接从字节中提取可读文本
            text = data.decode('utf-8', errors='ignore')
            
            # 查找所有可能的内容片段
            patterns = [
                r'"content":"([^"]+)"',
                r'content":"([^"]+)"',
                r'{"content":"([^"]+)"}',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text)
                content_parts.extend(matches)
            
            if content_parts:
                return ''.join(content_parts)
            
            # 最后的尝试：提取所有中文字符
            chinese_pattern = r'[\u4e00-\u9fff]+'
            chinese_matches = re.findall(chinese_pattern, text)
            if chinese_matches:
                return ''.join(chinese_matches)
            
        except Exception as e:
            print(f"备用解析失败: {e}")
        
        return ""

def test_parser():
    """测试解析器"""
    # 模拟乱码数据
    test_data = b'{"content":"\xe4\xbd\xa0\xe5\xa5\xbd"}'  # "你好"的UTF-8编码
    
    parser = EventStreamParser()
    result = parser.parse_binary_eventstream(test_data)
    print(f"测试结果: {result}")

if __name__ == "__main__":
    test_parser()
