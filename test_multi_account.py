#!/usr/bin/env python3
"""
多账号轮训功能测试脚本
用于验证Ki2API的多账号轮换功能
"""

import os
import sys
import asyncio
import httpx
from app import TokenManager

# 测试用的模拟token配置
TEST_TOKENS = [
    ("test_access_token_1", "test_refresh_token_1"),
    ("test_access_token_2", "test_refresh_token_2"), 
    ("test_access_token_3", "test_refresh_token_3"),
]

def setup_test_environment():
    """设置测试环境变量"""
    print("🔧 设置测试环境...")
    
    # 清除现有环境变量
    for key in list(os.environ.keys()):
        if key.startswith('KIRO_'):
            del os.environ[key]
    
    # 设置测试用的多账号配置
    for i, (access_token, refresh_token) in enumerate(TEST_TOKENS, 1):
        os.environ[f'KIRO_ACCESS_TOKEN_{i}'] = access_token
        os.environ[f'KIRO_REFRESH_TOKEN_{i}'] = refresh_token
        print(f"✅ 配置测试账号 {i}: {access_token[:20]}...")

def test_token_manager():
    """测试TokenManager的多账号功能"""
    print("\n🧪 测试TokenManager多账号功能...")
    
    # 重新导入以获取新的环境变量
    import importlib
    import app
    importlib.reload(app)
    
    token_manager = app.TokenManager()
    
    print(f"📊 加载的账号数量: {len(token_manager.access_tokens)}")
    print(f"📊 当前账号索引: {token_manager.current_index}")
    
    # 测试获取token
    for i in range(5):
        token = token_manager.get_token()
        print(f"🔑 第{i+1}次获取token: {token[:20] if token else 'None'}...")
        token_manager.rotate_to_next()
    
    # 测试失败标记
    print("\n🚨 测试账号失败标记...")
    test_token = token_manager.access_tokens[0]
    token_manager.mark_account_failed(test_token)
    print(f"❌ 失败账号列表: {token_manager.failed_accounts}")
    
    # 测试获取下一个可用账号
    next_account = token_manager.get_next_available_account()
    print(f"🔄 下一个可用账号索引: {next_account}")

async def test_api_rotation():
    """测试API调用的账号轮换（模拟）"""
    print("\n🌐 测试API调用轮换逻辑...")
    
    # 这里只是演示逻辑，不会真正调用API
    print("📝 注意：这是模拟测试，不会发起真实的API请求")
    print("✅ 多账号轮训逻辑已集成到call_kiro_api函数中")
    print("🔄 实际使用时会自动在账号间轮换")

def main():
    """主测试函数"""
    print("🚀 Ki2API 多账号轮训功能测试")
    print("=" * 50)
    
    try:
        # 设置测试环境
        setup_test_environment()
        
        # 测试TokenManager
        test_token_manager()
        
        # 测试API轮换逻辑
        asyncio.run(test_api_rotation())
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！多账号轮训功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
