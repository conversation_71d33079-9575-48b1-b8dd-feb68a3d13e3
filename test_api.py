#!/usr/bin/env python3
"""
测试Ki2API服务的脚本
"""

import requests
import json

def test_models_endpoint():
    """测试模型列表端点"""
    print("🧪 测试 /v1/models 端点...")
    
    try:
        response = requests.get(
            "http://localhost:8989/v1/models",
            headers={"Authorization": "Bearer ki2api-key-2024"},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_health_endpoint():
    """测试健康检查端点"""
    print("\n🧪 测试 /health 端点...")
    
    try:
        response = requests.get("http://localhost:8989/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_chat_endpoint():
    """测试聊天端点"""
    print("\n🧪 测试 /v1/chat/completions 端点...")
    
    try:
        data = {
            "model": "claude-sonnet-4-20250514",
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下自己"}
            ],
            "max_tokens": 100
        }
        
        response = requests.post(
            "http://localhost:8989/v1/chat/completions",
            headers={
                "Authorization": "Bearer ki2api-key-2024",
                "Content-Type": "application/json"
            },
            json=data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Ki2API 服务测试")
    print("=" * 50)
    
    # 测试各个端点
    tests = [
        ("健康检查", test_health_endpoint),
        ("模型列表", test_models_endpoint),
        ("聊天完成", test_chat_endpoint),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n📋 {name}测试:")
        success = test_func()
        results.append((name, success))
        print(f"{'✅ 通过' if success else '❌ 失败'}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    for name, success in results:
        print(f"  {name}: {'✅ 通过' if success else '❌ 失败'}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务运行正常")
    else:
        print("⚠️ 部分测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
