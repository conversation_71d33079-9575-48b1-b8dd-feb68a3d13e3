#!/usr/bin/env python3
"""
Kiro Token 分析和刷新测试工具
"""

import os
import json
import httpx
import asyncio
import base64
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

class TokenAnalyzer:
    def __init__(self):
        self.access_token = os.getenv("KIRO_ACCESS_TOKEN_1")
        self.refresh_token = os.getenv("KIRO_REFRESH_TOKEN_1")
        
        # 可能的刷新URL
        self.refresh_urls = [
            "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken",
            "https://auth.kiro.dev/refreshToken",
            "https://api.kiro.dev/auth/refresh",
            "https://kiro.dev/api/auth/refresh"
        ]
        
        # 测试用的API端点
        self.test_url = "https://codewhisperer.us-east-1.amazonaws.com/generateAssistantResponse"
        self.profile_arn = "arn:aws:codewhisperer:us-east-1:699475941385:profile/EHGA3GRVQMUK"

    def analyze_token_structure(self, token, token_type="Access"):
        """分析token结构"""
        print(f"\n🔍 分析{token_type} Token结构:")
        print(f"Token长度: {len(token)}")
        print(f"Token前缀: {token[:20]}...")
        print(f"Token后缀: ...{token[-20:]}")
        
        # 尝试解析JWT格式
        if '.' in token:
            parts = token.split('.')
            print(f"JWT部分数量: {len(parts)}")
            
            if len(parts) >= 2:
                try:
                    # 解码header
                    header_data = base64.b64decode(parts[0] + '==').decode('utf-8')
                    print(f"JWT Header: {header_data}")
                except:
                    print("无法解码JWT Header")
                
                try:
                    # 解码payload
                    payload_data = base64.b64decode(parts[1] + '==').decode('utf-8')
                    payload_json = json.loads(payload_data)
                    print(f"JWT Payload: {json.dumps(payload_json, indent=2)}")
                    
                    # 检查过期时间
                    if 'exp' in payload_json:
                        exp_time = datetime.fromtimestamp(payload_json['exp'])
                        now = datetime.now()
                        print(f"过期时间: {exp_time}")
                        print(f"当前时间: {now}")
                        print(f"是否过期: {'是' if now > exp_time else '否'}")
                        
                except Exception as e:
                    print(f"无法解码JWT Payload: {e}")

    async def test_access_token(self):
        """测试access token是否有效"""
        print("\n🧪 测试Access Token有效性...")
        
        if not self.access_token:
            print("❌ 未找到Access Token")
            return False
        
        # 构造测试请求
        test_request = {
            "profileArn": self.profile_arn,
            "conversationState": {
                "chatTriggerType": "MANUAL",
                "conversationId": "test-conversation",
                "currentMessage": {
                    "userInputMessage": {
                        "content": "Hello",
                        "modelId": "CLAUDE_SONNET_4_20250514_V1_0",
                        "origin": "AI_EDITOR",
                        "userInputMessageContext": {}
                    }
                },
                "history": []
            }
        }
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.test_url,
                    headers=headers,
                    json=test_request,
                    timeout=30
                )
                
                print(f"状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    print("✅ Access Token有效")
                    return True
                elif response.status_code == 401:
                    print("❌ Access Token无效 (401 Unauthorized)")
                    return False
                elif response.status_code == 403:
                    print("❌ Access Token过期或权限不足 (403 Forbidden)")
                    return False
                else:
                    print(f"⚠️ 未知状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:500]}...")
                    return False
                    
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

    async def test_refresh_token(self):
        """测试refresh token并尝试多个端点"""
        print("\n🔄 测试Refresh Token...")
        
        if not self.refresh_token:
            print("❌ 未找到Refresh Token")
            return None
        
        for i, refresh_url in enumerate(self.refresh_urls, 1):
            print(f"\n尝试端点 {i}: {refresh_url}")
            
            # 尝试不同的请求格式
            request_formats = [
                {"refreshToken": self.refresh_token},
                {"refresh_token": self.refresh_token},
                {"token": self.refresh_token},
                {
                    "grant_type": "refresh_token",
                    "refresh_token": self.refresh_token
                }
            ]
            
            for j, request_data in enumerate(request_formats, 1):
                print(f"  尝试格式 {j}: {list(request_data.keys())}")
                
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            refresh_url,
                            json=request_data,
                            timeout=30
                        )
                        
                        print(f"    状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("    ✅ 刷新成功!")
                            data = response.json()
                            print(f"    响应: {json.dumps(data, indent=4)}")
                            
                            new_access_token = data.get("accessToken") or data.get("access_token")
                            if new_access_token:
                                print(f"    🎉 获得新的Access Token: {new_access_token[:50]}...")
                                return new_access_token
                            else:
                                print("    ⚠️ 响应中没有新的Access Token")
                        else:
                            print(f"    ❌ 失败: {response.status_code}")
                            print(f"    错误信息: {response.text[:200]}...")
                            
                except Exception as e:
                    print(f"    ❌ 请求异常: {e}")
        
        return None

    async def comprehensive_analysis(self):
        """综合分析"""
        print("🚀 Kiro Token 综合分析")
        print("=" * 60)
        
        # 分析token结构
        if self.access_token:
            self.analyze_token_structure(self.access_token, "Access")
        
        if self.refresh_token:
            self.analyze_token_structure(self.refresh_token, "Refresh")
        
        # 测试access token
        access_valid = await self.test_access_token()
        
        # 如果access token无效，尝试刷新
        if not access_valid:
            print("\n🔄 Access Token无效，尝试刷新...")
            new_token = await self.test_refresh_token()
            
            if new_token:
                print(f"\n✅ 成功获取新token!")
                print("请更新您的.env文件:")
                print(f"KIRO_ACCESS_TOKEN_1={new_token}")
                
                # 测试新token
                self.access_token = new_token
                print("\n🧪 测试新token...")
                await self.test_access_token()
            else:
                print("\n❌ 无法刷新token，可能需要重新登录获取")
        
        print("\n" + "=" * 60)
        print("📋 建议:")
        if access_valid:
            print("✅ 当前token有效，可以正常使用")
        else:
            print("❌ 需要获取新的token:")
            print("1. 访问 https://kiro.dev 重新登录")
            print("2. 使用浏览器开发者工具获取新的token")
            print("3. 更新.env文件中的token配置")

async def main():
    analyzer = TokenAnalyzer()
    await analyzer.comprehensive_analysis()

if __name__ == "__main__":
    asyncio.run(main())
