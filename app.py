import os
import json
import time
import uuid
import httpx
import asyncio
import base64
from datetime import datetime, timezone
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Ki2API - Claude Sonnet 4 OpenAI Compatible API",
    description="Simple Docker-ready OpenAI-compatible API for Claude Sonnet 4",
    version="1.0.0"
)

# Configuration
API_KEY = os.getenv("API_KEY", "ki2api-key-2024")
KIRO_ACCESS_TOKEN = os.getenv("KIRO_ACCESS_TOKEN")
KIRO_REFRESH_TOKEN = os.getenv("KIRO_REFRESH_TOKEN")

# 多账号配置 - 支持最多10个账号
KIRO_ACCESS_TOKENS = []
KIRO_REFRESH_TOKENS = []

# 解析多账号环境变量
for i in range(1, 11):  # 支持ACCOUNT_1到ACCOUNT_10
    access_key = f"KIRO_ACCESS_TOKEN_{i}"
    refresh_key = f"KIRO_REFRESH_TOKEN_{i}"

    access_token = os.getenv(access_key)
    refresh_token = os.getenv(refresh_key)

    if access_token and refresh_token:
        KIRO_ACCESS_TOKENS.append(access_token)
        KIRO_REFRESH_TOKENS.append(refresh_token)

# 如果没有配置多账号，使用单账号配置
if not KIRO_ACCESS_TOKENS and KIRO_ACCESS_TOKEN and KIRO_REFRESH_TOKEN:
    KIRO_ACCESS_TOKENS.append(KIRO_ACCESS_TOKEN)
    KIRO_REFRESH_TOKENS.append(KIRO_REFRESH_TOKEN)

KIRO_BASE_URL = "https://codewhisperer.us-east-1.amazonaws.com/generateAssistantResponse"
PROFILE_ARN = "arn:aws:codewhisperer:us-east-1:************:profile/EHGA3GRVQMUK"

# Model mapping
MODEL_NAME = "claude-sonnet-4-********"
CODEWHISPERER_MODEL = "CLAUDE_SONNET_4_********_V1_0"

# Pydantic models
class ChatMessage(BaseModel):
    role: str
    content: Any  # 支持字符串或复杂对象格式

    def get_text_content(self) -> str:
        """提取文本内容，支持多种格式"""
        if isinstance(self.content, str):
            return self.content
        elif isinstance(self.content, list):
            # 处理包含多个文本块的格式
            text_parts = []
            for item in self.content:
                if isinstance(item, dict):
                    if item.get("type") == "text" and "text" in item:
                        text_parts.append(item["text"])
                    elif "text" in item:
                        text_parts.append(item["text"])
                elif isinstance(item, str):
                    text_parts.append(item)
            return "\n".join(text_parts)
        elif isinstance(self.content, dict):
            # 处理单个对象格式
            if "text" in self.content:
                return self.content["text"]
            elif "content" in self.content:
                return str(self.content["content"])

        # 兜底：转换为字符串
        return str(self.content)

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 4000
    stream: Optional[bool] = False

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class ChatCompletionStreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Dict[str, Any]]

# Token management with rotation support
class TokenManager:
    def __init__(self):
        self.access_tokens = KIRO_ACCESS_TOKENS.copy()
        self.refresh_tokens_list = KIRO_REFRESH_TOKENS.copy()  # 重命名避免冲突
        self.refresh_url = "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken"
        self.current_index = 0
        self.failed_accounts = set()  # 记录失败的账号索引
        self.permanently_failed = set()  # 记录永久失败的账号（refresh token失效）
        self.last_refresh_attempt = {}  # 记录最后一次刷新尝试时间

        if not self.access_tokens:
            print("❌ 未配置任何有效的账号token")
        else:
            print(f"✅ 已加载 {len(self.access_tokens)} 个账号用于轮训")

    def _decode_jwt_payload(self, token):
        """解码JWT token获取payload信息"""
        try:
            # JWT token格式: header.payload.signature
            parts = token.split('.')
            if len(parts) != 3:
                return None
            
            # 解码payload部分（base64）
            payload = parts[1]
            # 补齐base64填充
            payload += '=' * (4 - len(payload) % 4)
            decoded_bytes = base64.urlsafe_b64decode(payload)
            return json.loads(decoded_bytes.decode('utf-8'))
        except Exception:
            return None

    def _is_token_expired(self, token, buffer_minutes=5):
        """检查token是否过期（提前5分钟判断为过期）"""
        if not token:
            return True

        payload = self._decode_jwt_payload(token)
        if not payload or 'exp' not in payload:
            # 如果无法解析JWT，检查token长度和格式
            if len(token) < 50:  # token太短，可能无效
                return True
            # 对于非JWT格式的token，假设有效期1小时，检查创建时间
            return False  # 暂时假设有效

        exp_time = datetime.fromtimestamp(payload['exp'], tz=timezone.utc)
        current_time = datetime.now(tz=timezone.utc)
        buffer_time = current_time.timestamp() + (buffer_minutes * 60)

        return exp_time.timestamp() <= buffer_time

    def _should_attempt_refresh(self, account_index):
        """检查是否应该尝试刷新（避免频繁重试）"""
        if account_index in self.permanently_failed:
            return False
            
        last_attempt = self.last_refresh_attempt.get(account_index, 0)
        current_time = time.time()
        # 至少间隔30秒才重新尝试刷新
        return current_time - last_attempt > 30

    def get_next_available_account(self):
        """获取下一个可用账号的索引"""
        if not self.access_tokens:
            return None

        # 尝试找到一个未失败的账号
        attempts = 0
        while attempts < len(self.access_tokens):
            if (self.current_index not in self.failed_accounts and 
                self.current_index not in self.permanently_failed):
                return self.current_index

            # 轮换到下一个账号
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
            attempts += 1

        # 如果所有账号都失败了，检查是否有可以重试的临时失败账号
        available_accounts = set(range(len(self.access_tokens))) - self.permanently_failed
        if available_accounts:
            print("⚠️ 所有账号都临时失败了，重置临时失败状态重新尝试")
            self.failed_accounts.clear()
            self.current_index = min(available_accounts)
            return self.current_index
        
        print("❌ 所有账号都永久失败了，无法继续服务")
        return None

    async def refresh_tokens(self, account_index=None):
        """刷新指定账号的token"""
        if account_index is None:
            account_index = self.current_index

        if account_index >= len(self.refresh_tokens_list):
            print(f"❌ 账号索引 {account_index} 超出范围")
            return None

        # 检查是否应该尝试刷新
        if not self._should_attempt_refresh(account_index):
            print(f"❌ 账号 {account_index + 1} 刷新间隔未到或已永久失败，跳过")
            return None

        refresh_token = self.refresh_tokens_list[account_index]
        if not refresh_token:
            print(f"❌ 账号 {account_index + 1} 没有配置refresh token")
            self.permanently_failed.add(account_index)
            return None

        # 检查refresh token是否过期
        if self._is_token_expired(refresh_token, buffer_minutes=1):
            print(f"❌ 账号 {account_index + 1} 的refresh token已过期")
            self.permanently_failed.add(account_index)
            return None

        # 记录刷新尝试时间
        self.last_refresh_attempt[account_index] = time.time()

        try:
            async with httpx.AsyncClient() as client:
                print(f"🔄 正在刷新账号 {account_index + 1} 的token...")
                response = await client.post(
                    self.refresh_url,
                    json={"refreshToken": refresh_token},
                    timeout=30
                )

                if response.status_code == 400:
                    error_detail = "未知错误"
                    try:
                        error_data = response.json()
                        error_detail = error_data.get('message', error_data.get('error', str(error_data)))
                    except:
                        error_detail = response.text[:200] if response.text else "空响应"
                    
                    print(f"❌ 账号 {account_index + 1} refresh token无效或已过期: {error_detail}")
                    self.permanently_failed.add(account_index)
                    return None
                
                elif response.status_code == 401:
                    print(f"❌ 账号 {account_index + 1} refresh token认证失败，可能已被撤销")
                    self.permanently_failed.add(account_index)
                    return None
                
                elif response.status_code == 429:
                    print(f"❌ 账号 {account_index + 1} 刷新请求过于频繁，稍后重试")
                    # 这是临时错误，不标记为永久失败
                    return None
                
                elif response.status_code >= 500:
                    print(f"❌ 账号 {account_index + 1} 服务器错误 ({response.status_code})，稍后重试")
                    # 服务器错误，不标记为永久失败
                    return None

                response.raise_for_status()

                data = response.json()
                new_access_token = data.get("accessToken")
                if new_access_token:
                    self.access_tokens[account_index] = new_access_token
                    # 刷新成功，从失败列表中移除
                    self.failed_accounts.discard(account_index)
                    print(f"✅ 账号 {account_index + 1} token刷新成功")
                    
                    # 可选：更新refresh token（如果服务器返回了新的）
                    new_refresh_token = data.get("refreshToken")
                    if new_refresh_token:
                        self.refresh_tokens_list[account_index] = new_refresh_token
                        print(f"✅ 账号 {account_index + 1} refresh token也已更新")
                    
                    return new_access_token
                else:
                    print(f"❌ 账号 {account_index + 1} 刷新响应中没有accessToken")
                    return None

        except httpx.TimeoutException:
            print(f"❌ 账号 {account_index + 1} token刷新超时")
            return None
        except httpx.RequestError as e:
            print(f"❌ 账号 {account_index + 1} token刷新网络错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 账号 {account_index + 1} token刷新失败: {e}")
            # 对于未知错误，暂不标记为永久失败
            return None

    async def get_token(self):
        """获取当前可用的token，自动检查过期并刷新"""
        account_index = self.get_next_available_account()
        if account_index is None:
            return None

        token = self.access_tokens[account_index]

        # 检查access token是否快要过期（提前5分钟刷新）
        if self._is_token_expired(token, buffer_minutes=5):
            print(f"⚠️ 账号 {account_index + 1} 的access token即将过期，尝试主动刷新...")
            new_token = await self.refresh_tokens(account_index)
            if new_token:
                token = new_token
                print(f"✅ 账号 {account_index + 1} token主动刷新成功")
            else:
                print(f"❌ 账号 {account_index + 1} token主动刷新失败，将在API调用时重试")

        print(f"🔄 使用账号 {account_index + 1} (共{len(self.access_tokens)}个账号)")
        return token

    def get_token_sync(self):
        """同步版本的get_token，用于向后兼容"""
        account_index = self.get_next_available_account()
        if account_index is None:
            return None
        return self.access_tokens[account_index]

    def mark_account_failed(self, token, is_permanent=False):
        """标记使用指定token的账号为失败状态"""
        try:
            account_index = self.access_tokens.index(token)
            if is_permanent:
                self.permanently_failed.add(account_index)
                print(f"❌ 标记账号 {account_index + 1} 为永久失败状态")
            else:
                self.failed_accounts.add(account_index)
                print(f"❌ 标记账号 {account_index + 1} 为临时失败状态")

            # 轮换到下一个账号
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
        except ValueError:
            pass  # token不在列表中

    def rotate_to_next(self):
        """手动轮换到下一个账号"""
        if self.access_tokens:
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
            print(f"🔄 轮换到账号 {self.current_index + 1}")

token_manager = TokenManager()

# 后台任务：定期检查和刷新token
async def background_token_refresh():
    """后台任务：定期检查所有账号的token并主动刷新即将过期的token"""
    while True:
        try:
            print("🔍 后台检查token状态...")

            for i in range(len(token_manager.access_tokens)):
                if i in token_manager.permanently_failed:
                    continue

                token = token_manager.access_tokens[i]

                # 检查是否即将过期（提前10分钟刷新）
                if token_manager._is_token_expired(token, buffer_minutes=10):
                    print(f"🔄 后台刷新账号 {i + 1} 的token...")
                    new_token = await token_manager.refresh_tokens(i)
                    if new_token:
                        print(f"✅ 后台刷新账号 {i + 1} 成功")
                    else:
                        print(f"❌ 后台刷新账号 {i + 1} 失败")

            # 每5分钟检查一次
            await asyncio.sleep(300)

        except Exception as e:
            print(f"❌ 后台token刷新任务异常: {e}")
            await asyncio.sleep(60)  # 出错后1分钟后重试

# 启动后台任务
@app.on_event("startup")
async def startup_event():
    """应用启动时启动后台任务"""
    print("🚀 启动后台token刷新任务...")
    asyncio.create_task(background_token_refresh())

# Build CodeWhisperer request
def build_codewhisperer_request(messages: List[ChatMessage]):
    conversation_id = str(uuid.uuid4())
    
    # Extract system prompt and user messages
    system_prompt = ""
    user_messages = []
    
    for msg in messages:
        if msg.role == "system":
            system_prompt = msg.get_text_content()
        else:
            user_messages.append(msg)
    
    if not user_messages:
        raise HTTPException(status_code=400, detail="No user messages found")
    
    # Build history
    history = []
    for i in range(0, len(user_messages) - 1, 2):
        if i + 1 < len(user_messages):
            history.append({
                "userInputMessage": {
                    "content": user_messages[i].get_text_content(),
                    "modelId": CODEWHISPERER_MODEL,
                    "origin": "AI_EDITOR"
                }
            })
            history.append({
                "assistantResponseMessage": {
                    "content": user_messages[i + 1].get_text_content(),
                    "toolUses": []
                }
            })
    
    # Build current message
    current_message = user_messages[-1]
    content = current_message.get_text_content()
    if system_prompt:
        content = f"{system_prompt}\n\n{content}"
    
    return {
        "profileArn": PROFILE_ARN,
        "conversationState": {
            "chatTriggerType": "MANUAL",
            "conversationId": conversation_id,
            "currentMessage": {
                "userInputMessage": {
                    "content": content,
                    "modelId": CODEWHISPERER_MODEL,
                    "origin": "AI_EDITOR",
                    "userInputMessageContext": {}
                }
            },
            "history": history
        }
    }

# Make API call to Kiro/CodeWhisperer with account rotation
async def call_kiro_api(messages: List[ChatMessage], stream: bool = False):
    request_data = build_codewhisperer_request(messages)

    # 尝试所有可用账号
    max_attempts = len(token_manager.access_tokens) if token_manager.access_tokens else 1

    for attempt in range(max_attempts):
        token = await token_manager.get_token()
        if not token:
            if attempt == 0:
                raise HTTPException(status_code=401, detail="No access token available")
            else:
                continue

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "text/event-stream" if stream else "application/json"
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    KIRO_BASE_URL,
                    headers=headers,
                    json=request_data,
                    timeout=120
                )

                if response.status_code == 403:
                    print(f"🔄 账号token过期，尝试刷新...")
                    # 尝试刷新当前账号的token
                    current_account_index = None
                    try:
                        current_account_index = token_manager.access_tokens.index(token)
                    except ValueError:
                        pass
                    
                    new_token = await token_manager.refresh_tokens(current_account_index)
                    if new_token:
                        headers["Authorization"] = f"Bearer {new_token}"
                        print(f"🔄 使用刷新后的token重新尝试API调用...")
                        response = await client.post(
                            KIRO_BASE_URL,
                            headers=headers,
                            json=request_data,
                            timeout=120
                        )

                    # 如果刷新后仍然失败，标记账号失败并尝试下一个
                    if response.status_code == 403:
                        print(f"❌ 账号刷新后仍然失败，切换到下一个账号")
                        token_manager.mark_account_failed(token, is_permanent=True)
                        token_manager.rotate_to_next()
                        continue

                response.raise_for_status()
                print(f"✅ API调用成功")
                return response

        except httpx.HTTPStatusError as e:
            if e.response.status_code in [401, 403]:
                print(f"❌ 账号认证失败 (HTTP {e.response.status_code})，尝试下一个账号")
                # 401/403可能是token问题，标记为临时失败给刷新机制一次机会
                token_manager.mark_account_failed(token, is_permanent=False)
                token_manager.rotate_to_next()
                continue
            else:
                print(f"❌ HTTP错误: {e}")
                if attempt == max_attempts - 1:
                    raise HTTPException(status_code=e.response.status_code, detail=f"API call failed: {str(e)}")
                continue

        except Exception as e:
            print(f"❌ 网络或其他错误: {e}")
            if attempt == max_attempts - 1:
                raise HTTPException(status_code=503, detail=f"API call failed: {str(e)}")
            # 网络错误等，尝试下一个账号
            token_manager.mark_account_failed(token, is_permanent=False)
            token_manager.rotate_to_next()
            continue

    raise HTTPException(status_code=503, detail="All accounts failed")

# API endpoints
@app.get("/v1/models")
async def list_models():
    return {
        "object": "list",
        "data": [
            {
                "id": MODEL_NAME,
                "object": "model",
                "created": int(time.time()),
                "owned_by": "ki2api"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    if request.model != MODEL_NAME:
        raise HTTPException(status_code=400, detail=f"Only {MODEL_NAME} is supported")
    
    if request.stream:
        return await create_streaming_response(request)
    else:
        return await create_non_streaming_response(request)

def parse_eventstream_response(response_data: bytes) -> str:
    """解析AWS EventStream格式的响应数据"""
    import re
    import struct

    content_parts = []

    try:
        # 方法1: 直接正则匹配JSON内容
        json_pattern = rb'\{"content":"([^"]+)"\}'
        matches = re.findall(json_pattern, response_data)

        if matches:
            for match in matches:
                try:
                    # 解码UTF-8内容
                    text = match.decode('utf-8')
                    content_parts.append(text)
                except UnicodeDecodeError:
                    # 尝试其他编码
                    try:
                        text = match.decode('latin-1')
                        content_parts.append(text)
                    except:
                        continue

        if content_parts:
            return ''.join(content_parts)

        # 方法2: 尝试从文本中提取
        try:
            text_data = response_data.decode('utf-8', errors='ignore')
            text_pattern = r'"content":"([^"]+)"'
            text_matches = re.findall(text_pattern, text_data)
            if text_matches:
                return ''.join(text_matches)
        except:
            pass

        # 方法3: 查找所有可能的中文内容
        try:
            text_data = response_data.decode('utf-8', errors='ignore')
            # 提取中文字符
            chinese_pattern = r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]+'
            chinese_matches = re.findall(chinese_pattern, text_data)
            if chinese_matches:
                return ''.join(chinese_matches)
        except:
            pass

    except Exception as e:
        print(f"解析EventStream响应时出错: {e}")

    return ""

async def create_non_streaming_response(request: ChatCompletionRequest):
    response = await call_kiro_api(request.messages, stream=False)

    # 获取原始二进制数据
    raw_data = response.content  # 使用content而不是text

    # 解析EventStream格式的响应
    parsed_content = parse_eventstream_response(raw_data)

    # 如果解析失败，尝试其他方法
    if not parsed_content:
        try:
            # 尝试作为普通JSON解析
            json_data = response.json()
            parsed_content = json_data.get('content', response.text)
        except:
            # 最后的备用方案
            parsed_content = response.text

    # 如果内容为空，提供默认响应
    if not parsed_content.strip():
        parsed_content = "抱歉，我无法生成回复。"

    return ChatCompletionResponse(
        model=MODEL_NAME,
        choices=[{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": parsed_content
            },
            "finish_reason": "stop"
        }],
        usage={
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
    )

async def create_streaming_response(request: ChatCompletionRequest):
    response = await call_kiro_api(request.messages, stream=True)
    
    async def generate():
        # Send initial response
        initial_chunk = {
            'id': f'chatcmpl-{uuid.uuid4()}',
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': MODEL_NAME,
            'choices': [{
                'index': 0,
                'delta': {'role': 'assistant'},
                'finish_reason': None
            }]
        }
        yield f"data: {json.dumps(initial_chunk)}\n\n"
        
        # Read response and stream content
        content = ""
        async for line in response.aiter_lines():
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    if 'content' in data:
                        content += data['content']
                        chunk = {
                            'id': f'chatcmpl-{uuid.uuid4()}',
                            'object': 'chat.completion.chunk',
                            'created': int(time.time()),
                            'model': MODEL_NAME,
                            'choices': [{
                                'index': 0,
                                'delta': {'content': data['content']},
                                'finish_reason': None
                            }]
                        }
                        yield f"data: {json.dumps(chunk)}\n\n"
                except:
                    continue
        
        # Send final response
        final_chunk = {
            'id': f'chatcmpl-{uuid.uuid4()}',
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': MODEL_NAME,
            'choices': [{
                'index': 0,
                'delta': {},
                'finish_reason': 'stop'
            }]
        }
        yield f"data: {json.dumps(final_chunk)}\n\n"
        
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(generate(), media_type="text/event-stream")

# Health check
@app.get("/health")
async def health_check():
    return {"status": "ok", "service": "ki2api"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8989)