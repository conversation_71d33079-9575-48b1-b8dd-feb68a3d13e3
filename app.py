import os
import json
import time
import uuid
import httpx
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Ki2API - Claude Sonnet 4 OpenAI Compatible API",
    description="Simple Docker-ready OpenAI-compatible API for Claude Sonnet 4",
    version="1.0.0"
)

# Configuration
API_KEY = os.getenv("API_KEY", "ki2api-key-2024")
KIRO_ACCESS_TOKEN = os.getenv("KIRO_ACCESS_TOKEN")
KIRO_REFRESH_TOKEN = os.getenv("KIRO_REFRESH_TOKEN")

# 多账号配置 - 支持最多10个账号
KIRO_ACCESS_TOKENS = []
KIRO_REFRESH_TOKENS = []

# 解析多账号环境变量
for i in range(1, 11):  # 支持ACCOUNT_1到ACCOUNT_10
    access_key = f"KIRO_ACCESS_TOKEN_{i}"
    refresh_key = f"KIRO_REFRESH_TOKEN_{i}"

    access_token = os.getenv(access_key)
    refresh_token = os.getenv(refresh_key)

    if access_token and refresh_token:
        KIRO_ACCESS_TOKENS.append(access_token)
        KIRO_REFRESH_TOKENS.append(refresh_token)

# 如果没有配置多账号，使用单账号配置
if not KIRO_ACCESS_TOKENS and KIRO_ACCESS_TOKEN and KIRO_REFRESH_TOKEN:
    KIRO_ACCESS_TOKENS.append(KIRO_ACCESS_TOKEN)
    KIRO_REFRESH_TOKENS.append(KIRO_REFRESH_TOKEN)

KIRO_BASE_URL = "https://codewhisperer.us-east-1.amazonaws.com/generateAssistantResponse"
PROFILE_ARN = "arn:aws:codewhisperer:us-east-1:************:profile/EHGA3GRVQMUK"

# Model mapping
MODEL_NAME = "claude-sonnet-4-********"
CODEWHISPERER_MODEL = "CLAUDE_SONNET_4_********_V1_0"

# Pydantic models
class ChatMessage(BaseModel):
    role: str
    content: Any  # 支持字符串或复杂对象格式

    def get_text_content(self) -> str:
        """提取文本内容，支持多种格式"""
        if isinstance(self.content, str):
            return self.content
        elif isinstance(self.content, list):
            # 处理包含多个文本块的格式
            text_parts = []
            for item in self.content:
                if isinstance(item, dict):
                    if item.get("type") == "text" and "text" in item:
                        text_parts.append(item["text"])
                    elif "text" in item:
                        text_parts.append(item["text"])
                elif isinstance(item, str):
                    text_parts.append(item)
            return "\n".join(text_parts)
        elif isinstance(self.content, dict):
            # 处理单个对象格式
            if "text" in self.content:
                return self.content["text"]
            elif "content" in self.content:
                return str(self.content["content"])

        # 兜底：转换为字符串
        return str(self.content)

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 4000
    stream: Optional[bool] = False

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class ChatCompletionStreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4()}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[Dict[str, Any]]

# Token management with rotation support
class TokenManager:
    def __init__(self):
        self.access_tokens = KIRO_ACCESS_TOKENS.copy()
        self.refresh_tokens = KIRO_REFRESH_TOKENS.copy()
        self.refresh_url = "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken"
        self.current_index = 0
        self.failed_accounts = set()  # 记录失败的账号索引

        if not self.access_tokens:
            print("❌ 未配置任何有效的账号token")
        else:
            print(f"✅ 已加载 {len(self.access_tokens)} 个账号用于轮训")

    def get_next_available_account(self):
        """获取下一个可用账号的索引"""
        if not self.access_tokens:
            return None

        # 尝试找到一个未失败的账号
        attempts = 0
        while attempts < len(self.access_tokens):
            if self.current_index not in self.failed_accounts:
                return self.current_index

            # 轮换到下一个账号
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
            attempts += 1

        # 如果所有账号都失败了，清空失败记录重新开始
        if len(self.failed_accounts) == len(self.access_tokens):
            print("⚠️ 所有账号都失败了，重置失败状态重新尝试")
            self.failed_accounts.clear()
            return self.current_index

        return None

    async def refresh_tokens(self, account_index=None):
        """刷新指定账号的token"""
        if account_index is None:
            account_index = self.current_index

        if account_index >= len(self.refresh_tokens):
            return None

        refresh_token = self.refresh_tokens[account_index]
        if not refresh_token:
            return None

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.refresh_url,
                    json={"refreshToken": refresh_token},
                    timeout=30
                )
                response.raise_for_status()

                data = response.json()
                new_access_token = data.get("accessToken")
                if new_access_token:
                    self.access_tokens[account_index] = new_access_token
                    # 刷新成功，从失败列表中移除
                    self.failed_accounts.discard(account_index)
                    print(f"✅ 账号 {account_index + 1} token刷新成功")
                    return new_access_token

        except Exception as e:
            print(f"❌ 账号 {account_index + 1} token刷新失败: {e}")
            self.failed_accounts.add(account_index)

        return None

    def get_token(self):
        """获取当前可用的token"""
        account_index = self.get_next_available_account()
        if account_index is None:
            return None

        token = self.access_tokens[account_index]
        print(f"🔄 使用账号 {account_index + 1} (共{len(self.access_tokens)}个账号)")
        return token

    def mark_account_failed(self, token):
        """标记使用指定token的账号为失败状态"""
        try:
            account_index = self.access_tokens.index(token)
            self.failed_accounts.add(account_index)
            print(f"❌ 标记账号 {account_index + 1} 为失败状态")

            # 轮换到下一个账号
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
        except ValueError:
            pass  # token不在列表中

    def rotate_to_next(self):
        """手动轮换到下一个账号"""
        if self.access_tokens:
            self.current_index = (self.current_index + 1) % len(self.access_tokens)
            print(f"🔄 轮换到账号 {self.current_index + 1}")

token_manager = TokenManager()

# Build CodeWhisperer request
def build_codewhisperer_request(messages: List[ChatMessage]):
    conversation_id = str(uuid.uuid4())
    
    # Extract system prompt and user messages
    system_prompt = ""
    user_messages = []
    
    for msg in messages:
        if msg.role == "system":
            system_prompt = msg.get_text_content()
        else:
            user_messages.append(msg)
    
    if not user_messages:
        raise HTTPException(status_code=400, detail="No user messages found")
    
    # Build history
    history = []
    for i in range(0, len(user_messages) - 1, 2):
        if i + 1 < len(user_messages):
            history.append({
                "userInputMessage": {
                    "content": user_messages[i].get_text_content(),
                    "modelId": CODEWHISPERER_MODEL,
                    "origin": "AI_EDITOR"
                }
            })
            history.append({
                "assistantResponseMessage": {
                    "content": user_messages[i + 1].get_text_content(),
                    "toolUses": []
                }
            })
    
    # Build current message
    current_message = user_messages[-1]
    content = current_message.get_text_content()
    if system_prompt:
        content = f"{system_prompt}\n\n{content}"
    
    return {
        "profileArn": PROFILE_ARN,
        "conversationState": {
            "chatTriggerType": "MANUAL",
            "conversationId": conversation_id,
            "currentMessage": {
                "userInputMessage": {
                    "content": content,
                    "modelId": CODEWHISPERER_MODEL,
                    "origin": "AI_EDITOR",
                    "userInputMessageContext": {}
                }
            },
            "history": history
        }
    }

# Make API call to Kiro/CodeWhisperer with account rotation
async def call_kiro_api(messages: List[ChatMessage], stream: bool = False):
    request_data = build_codewhisperer_request(messages)

    # 尝试所有可用账号
    max_attempts = len(token_manager.access_tokens) if token_manager.access_tokens else 1

    for attempt in range(max_attempts):
        token = token_manager.get_token()
        if not token:
            if attempt == 0:
                raise HTTPException(status_code=401, detail="No access token available")
            else:
                continue

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "text/event-stream" if stream else "application/json"
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    KIRO_BASE_URL,
                    headers=headers,
                    json=request_data,
                    timeout=120
                )

                if response.status_code == 403:
                    print(f"🔄 账号token过期，尝试刷新...")
                    # 尝试刷新当前账号的token
                    new_token = await token_manager.refresh_tokens()
                    if new_token:
                        headers["Authorization"] = f"Bearer {new_token}"
                        response = await client.post(
                            KIRO_BASE_URL,
                            headers=headers,
                            json=request_data,
                            timeout=120
                        )

                    # 如果刷新后仍然失败，标记账号失败并尝试下一个
                    if response.status_code == 403:
                        print(f"❌ 账号刷新后仍然失败，切换到下一个账号")
                        token_manager.mark_account_failed(token)
                        token_manager.rotate_to_next()
                        continue

                response.raise_for_status()
                print(f"✅ API调用成功")
                return response

        except httpx.HTTPStatusError as e:
            if e.response.status_code in [401, 403]:
                print(f"❌ 账号认证失败 (HTTP {e.response.status_code})，尝试下一个账号")
                token_manager.mark_account_failed(token)
                token_manager.rotate_to_next()
                continue
            else:
                print(f"❌ HTTP错误: {e}")
                if attempt == max_attempts - 1:
                    raise HTTPException(status_code=e.response.status_code, detail=f"API call failed: {str(e)}")
                continue

        except Exception as e:
            print(f"❌ 网络或其他错误: {e}")
            if attempt == max_attempts - 1:
                raise HTTPException(status_code=503, detail=f"API call failed: {str(e)}")
            # 尝试下一个账号
            token_manager.rotate_to_next()
            continue

    raise HTTPException(status_code=503, detail="All accounts failed")

# API endpoints
@app.get("/v1/models")
async def list_models():
    return {
        "object": "list",
        "data": [
            {
                "id": MODEL_NAME,
                "object": "model",
                "created": int(time.time()),
                "owned_by": "ki2api"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    if request.model != MODEL_NAME:
        raise HTTPException(status_code=400, detail=f"Only {MODEL_NAME} is supported")
    
    if request.stream:
        return await create_streaming_response(request)
    else:
        return await create_non_streaming_response(request)

async def create_non_streaming_response(request: ChatCompletionRequest):
    response = await call_kiro_api(request.messages, stream=False)
    
    # Parse response
    response_text = response.text
    
    return ChatCompletionResponse(
        model=MODEL_NAME,
        choices=[{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": response_text
            },
            "finish_reason": "stop"
        }],
        usage={
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
    )

async def create_streaming_response(request: ChatCompletionRequest):
    response = await call_kiro_api(request.messages, stream=True)
    
    async def generate():
        # Send initial response
        initial_chunk = {
            'id': f'chatcmpl-{uuid.uuid4()}',
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': MODEL_NAME,
            'choices': [{
                'index': 0,
                'delta': {'role': 'assistant'},
                'finish_reason': None
            }]
        }
        yield f"data: {json.dumps(initial_chunk)}\n\n"
        
        # Read response and stream content
        content = ""
        async for line in response.aiter_lines():
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    if 'content' in data:
                        content += data['content']
                        chunk = {
                            'id': f'chatcmpl-{uuid.uuid4()}',
                            'object': 'chat.completion.chunk',
                            'created': int(time.time()),
                            'model': MODEL_NAME,
                            'choices': [{
                                'index': 0,
                                'delta': {'content': data['content']},
                                'finish_reason': None
                            }]
                        }
                        yield f"data: {json.dumps(chunk)}\n\n"
                except:
                    continue
        
        # Send final response
        final_chunk = {
            'id': f'chatcmpl-{uuid.uuid4()}',
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': MODEL_NAME,
            'choices': [{
                'index': 0,
                'delta': {},
                'finish_reason': 'stop'
            }]
        }
        yield f"data: {json.dumps(final_chunk)}\n\n"
        
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(generate(), media_type="text/event-stream")

# Health check
@app.get("/health")
async def health_check():
    return {"status": "ok", "service": "ki2api"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8989)