#!/usr/bin/env python3
"""
重置账号失败状态的脚本
"""

import requests
import json
import os

def reset_account_status():
    """重置账号失败状态"""
    print("🔄 重置账号失败状态...")
    
    # 这里我们需要重启服务来重置内存中的失败状态
    print("需要重启Ki2API服务来重置账号状态")
    
def test_current_token():
    """测试当前token是否有效"""
    print("🧪 测试当前token状态...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8989/health", timeout=5)
        print(f"健康检查: {response.status_code}")
        
        # 测试Claude API端点
        data = {
            "model": "claude-sonnet-4-********",
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "max_tokens": 50
        }
        
        response = requests.post(
            "http://localhost:8989/v1/messages",
            headers={"Content-Type": "application/json"},
            json=data,
            timeout=30
        )
        
        print(f"Claude API测试: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {result}")
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_current_token()
