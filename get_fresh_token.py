#!/usr/bin/env python3
"""
获取新的Kiro token的指导脚本
"""

import os
import json
from pathlib import Path

def check_token_file():
    """检查本地token文件"""
    print("🔍 检查本地Kiro token文件...")
    
    # 可能的token文件路径
    possible_paths = [
        Path.home() / ".aws" / "sso" / "cache" / "kiro-auth-token.json",
        Path.home() / ".kiro" / "auth-token.json",
        Path.home() / ".config" / "kiro" / "auth-token.json"
    ]
    
    for token_path in possible_paths:
        print(f"检查路径: {token_path}")
        if token_path.exists():
            try:
                with open(token_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                access_token = data.get('accessToken', '')
                refresh_token = data.get('refreshToken', '')
                expires_at = data.get('expiresAt', '')
                
                print(f"✅ 找到token文件: {token_path}")
                print(f"Access Token: {access_token[:50]}..." if access_token else "❌ 缺少Access Token")
                print(f"Refresh Token: {refresh_token[:50]}..." if refresh_token else "❌ 缺少Refresh Token")
                print(f"过期时间: {expires_at}")
                
                if access_token and refresh_token:
                    return access_token, refresh_token
                    
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        else:
            print(f"❌ 文件不存在")
    
    return None, None

def get_token_instructions():
    """提供获取token的说明"""
    print("\n" + "="*60)
    print("📋 如何获取新的Kiro Token")
    print("="*60)
    
    print("""
1. 🌐 访问 Kiro 官网
   打开浏览器访问: https://kiro.dev

2. 🔐 登录您的账号
   使用您的GitHub、Google或其他方式登录

3. 🔧 打开开发者工具
   - Chrome/Edge: 按F12或右键选择"检查"
   - Firefox: 按F12或右键选择"检查元素"

4. 📡 查找网络请求
   - 切换到"Network"(网络)标签
   - 在网站上进行任何操作(如发送消息)
   - 查找包含"Authorization"头的请求

5. 📋 复制Token信息
   在请求头中找到类似这样的信息:
   Authorization: Bearer aoaAAAAAGh_S2wYPaN...
   
6. 🔄 查找Refresh Token
   在响应或其他请求中查找refreshToken字段

7. ⚙️ 更新配置
   将获取的token更新到.env文件中
""")

def update_env_file():
    """交互式更新.env文件"""
    print("\n🔧 更新.env文件")
    print("-" * 30)
    
    access_token = input("请输入新的Access Token: ").strip()
    refresh_token = input("请输入新的Refresh Token: ").strip()
    
    if access_token and refresh_token:
        env_content = f"""# Ki2API 多账号轮训配置
# 更新时间: {os.popen('date').read().strip()}

# 基础配置
API_KEY=ki2api-key-2024

# 账号1 - 已更新
KIRO_ACCESS_TOKEN_1={access_token}
KIRO_REFRESH_TOKEN_1={refresh_token}

# 账号2 - 待配置
# KIRO_ACCESS_TOKEN_2=your_access_token_2_here
# KIRO_REFRESH_TOKEN_2=your_refresh_token_2_here

# 账号3 - 待配置
# KIRO_ACCESS_TOKEN_3=your_access_token_3_here
# KIRO_REFRESH_TOKEN_3=your_refresh_token_3_here
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ .env文件已更新")
        return True
    else:
        print("❌ Token信息不完整")
        return False

def main():
    """主函数"""
    print("🚀 Kiro Token 获取助手")
    print("=" * 50)
    
    # 检查现有token
    access_token, refresh_token = check_token_file()
    
    if access_token and refresh_token:
        print("\n✅ 找到现有token，您可以:")
        print("1. 直接使用现有token")
        print("2. 获取新的token")
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            # 使用现有token更新.env
            env_content = f"""# Ki2API 配置
API_KEY=ki2api-key-2024
KIRO_ACCESS_TOKEN_1={access_token}
KIRO_REFRESH_TOKEN_1={refresh_token}
"""
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)
            print("✅ 已使用现有token更新.env文件")
            return
    
    # 提供获取新token的说明
    get_token_instructions()
    
    # 交互式更新
    if input("\n是否现在更新.env文件? (y/n): ").lower() == 'y':
        update_env_file()

if __name__ == "__main__":
    main()
